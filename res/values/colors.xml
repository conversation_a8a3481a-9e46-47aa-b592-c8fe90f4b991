<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2007 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android">

    <color name="divider_color">#20ffffff</color>
    <color name="title_color">@android:color/holo_blue_light</color>
    <color name="setup_wizard_wifi_color_dark">#89ffffff</color>
    <color name="setup_wizard_wifi_color_light">#89000000</color>

    <color name="lock_pattern_background">#00000000</color>

    <color name="fingerprint_title_area_bg">?android:attr/colorAccent</color>
    <color name="fingerprint_title_color">#ffffffff</color>
    <color name="fingerprint_message_color">#de000000</color>

    <color name="running_processes_system_ram">#ff384248</color>
    <color name="running_processes_free_ram">#ffced7db</color>

    <color name="sim_noitification">@*android:color/accent_device_default_light</color>

    <color name="confirm_device_credential_transparent_black">#60000000</color>

    <!-- Accent color that matches the settings launcher icon -->
    <color name="icon_accent">#ffabffec</color>

    <!-- Accessibility SUW colors -->
    <color name="material_blue_500">#4285F4</color>
    <color name="material_blue_700">#3367D6</color>
    <color name="material_grey_100">#f5f5f5</color>
    <color name="material_grey_200">#ffffff</color>

    <color name="message_text_incoming">#E4E3DA</color>
    <color name="message_text_outgoing">#1B1C17</color>
    <color name="timestamp_text_outgoing">#99323232</color>
    <color name="timestamp_text_incoming">#99ffffff</color>
    <color name="message_bubble_incoming">#52534D</color>
    <color name="message_bubble_outgoing">#C7C8B7</color>
    <color name="message_icon_background_incoming">#E6F451</color>
    <color name="message_icon_text_incoming">#ffffffff</color>
    <color name="message_icon_background_outgoing">#FBBC04</color>
    <color name="message_icon_text_outgoing">#ffffffff</color>
    <color name="message_icon_color">#DADADA</color>

    <color name="usage_graph_dots">#B0BEC5</color>

    <!-- Gestures settings -->
    <color name="gestures_setting_background_color">#ffffff</color>

    <!-- Color for the background of the shortcut icons.-->
    <color name="shortcut_background">#fff5f5f5</color>

    <!-- The fallback color for tinting icons. Only used when colorControlNormal is unavailable -->
    <color name="fallback_tintColor">#89000000</color>

    <!-- Dashboard/homepage icon background colors -->
    <color name="homepage_network_background">#2196F3</color>
    <color name="homepage_connected_device_background">#72B70F</color>
    <color name="homepage_app_and_notification_background">#FF7E0F</color>
    <color name="homepage_battery_background">#258982</color>
    <color name="homepage_display_background">#FFA727</color>
    <color name="homepage_sound_background">#01B1AF</color>
    <color name="homepage_storage_background">#C14CE6</color>
    <color name="homepage_security_background">#0F9D58</color>
    <color name="homepage_accounts_background">#F15B8D</color>
    <color name="homepage_accessibility_background">#5011C1</color>
    <color name="homepage_emergency_background">#F28B82</color>
    <color name="homepage_system_background">#9E9E9E</color>
    <color name="homepage_support_background">#26459C</color>
    <color name="homepage_generic_icon_background">#1A73E8</color>
    <color name="homepage_location_background">#2EC7DC</color>
    <color name="homepage_about_background">#6F86DA</color>
    <color name="homepage_privacy_background">#5E97F6</color>
    <color name="homepage_wallpaper_background">#E51AD1</color>
    <color name="homepage_notification_background">#DD4C9D</color>

    <color name="contextual_card_dismissal_background">@*android:color/material_grey_100</color>
    <color name="contextual_card_background">@*android:color/background_device_default_light</color>
    <!-- End of dashboard/homepage icon background colors -->

    <color name="switchbar_background_color">@*android:color/material_grey_600</color>
    <color name="switchbar_switch_track_tint">#BFFFFFFF</color>
    <color name="switchbar_switch_thumb_tint">@android:color/white</color>

    <color name="battery_good_color_light">#43a047</color> <!-- Material Green 600 -->
    <color name="battery_maybe_color_light">#ef6c00</color> <!-- Material Orange 800 -->
    <color name="battery_bad_color_light">#f44336</color> <!-- Material Red 500 -->
    <color name="battery_good_color_dark">#4caf50</color> <!-- Material Green 500 -->
    <color name="battery_maybe_color_dark">#fdd835</color> <!-- Material Yellow 600 -->
    <color name="battery_bad_color_dark">#f44336</color> <!-- Material Red 500 -->

    <!-- TODO: Figure out colors -->
    <color name="face_anim_particle_color_1">#ff00bcd4</color> <!-- Material Cyan 500 -->
    <color name="face_anim_particle_color_2">#ffef6c00</color> <!-- Material Orange 800 -->
    <color name="face_anim_particle_color_3">#ff4caf50</color> <!-- Material Green 500 -->
    <color name="face_anim_particle_color_4">#fffdd835</color> <!-- Material Yellow 600 -->
    <color name="face_anim_particle_error">#ff9e9e9e</color> <!-- Material Gray 500 -->

    <!-- Face and fingerprint enrollment -->
    <color name="biometric_enroll_intro_color_bar">#1e8e3e</color>
    <color name="biometric_enroll_intro_color_icon">#1a73e8</color>
    <color name="biometric_enroll_intro_color_outline">#e3e3e3</color>

    <!-- notification settings -->
    <color name="notification_block_color">#ffff0000</color>
    <color name="notification_silence_color">#FF32c1de</color>
    <color name="notification_alert_color">#FFF87B2B</color>
    <color name="notification_importance_button_unselected">#DADCE0</color>
    <color name="notification_importance_button_selected">#1967D2</color> <!-- material blue 700 -->
    <color name="notification_importance_selection_bg">#FFFFFF</color>
    <color name="notification_history_background">?android:attr/colorBackgroundFloating</color>

    <!-- launcher icon color -->
    <color name="icon_launcher_setting_color">@*android:color/accent_device_default_light</color>

    <!-- QR code scanner colors -->
    <color name="qr_corner_line_color">#ffdadce0</color>
    <color name="qr_focused_corner_line_color">#ff1a73e8</color>
    <color name="qr_background_color">#b3ffffff</color> <!-- 70% white transparency -->
    <!-- End of QR code scanner colors -->

    <!-- Search bar background color -->
    <color name="search_bar_background">?androidprv:attr/colorSurfaceHighlight</color>

    <color name="face_enroll_icon_color">#42a5f5</color> <!-- Blue 400 -->

    <color name="back_gesture_indicator">#4182ef</color>

    <!-- Palette list preference colors. -->
    <color name="palette_list_gradient_background">@android:color/white</color>
    <color name="palette_list_color_red">#d93025</color> <!-- Material Red 600 -->
    <color name="palette_list_color_orange">#e8710a</color> <!-- Material Orange 600 -->
    <color name="palette_list_color_yellow">#f9ab00</color> <!-- Material Yellow 600 -->
    <color name="palette_list_color_green">#1e8e3e</color> <!-- Material Green 600 -->
    <color name="palette_list_color_cyan">#12b5cb</color> <!-- Material Cyan 600 -->
    <color name="palette_list_color_blue">#1a73e8</color> <!-- Material Blue 600 -->
    <color name="palette_list_color_purple">#9334e6</color> <!-- Material Purple 600 -->
    <color name="palette_list_color_gray">#80868b</color> <!-- Material Gray 600 -->

    <!-- Palette list preference dark mode colors. -->
    <color name="palette_list_dark_mode_color_red">#f28b82</color> <!-- Material Red 300 -->
    <color name="palette_list_dark_mode_color_orange">#fcad70</color> <!-- Material Orange 300 -->
    <color name="palette_list_dark_mode_color_yellow">#fdd663</color> <!-- Material Yellow 300 -->
    <color name="palette_list_dark_mode_color_green">#81c995</color> <!-- Material Green 300 -->
    <color name="palette_list_dark_mode_color_cyan">#78d9ec</color> <!-- Material Cyan 300 -->
    <color name="palette_list_dark_mode_color_blue">#8AB4F8</color> <!-- Material Blue 300 -->
    <color name="palette_list_dark_mode_color_purple">#c58af9</color> <!-- Material Purple 300 -->
    <color name="palette_list_dark_mode_color_gray">#dadce0</color> <!-- Material Gray 300 -->

    <!-- SIM colors -->
    <color name="SIM_color_teal">#ff00796b</color> <!-- Material Teal 700 -->
    <color name="SIM_color_blue">#ff3367d6</color> <!-- Material Blue 700 -->
    <color name="SIM_color_indigo">#ff303f9f</color> <!-- Material Indigo 700 -->
    <color name="SIM_color_purple">#ff7b1fa2</color> <!-- Material Purple 700 -->
    <color name="SIM_color_pink">#ffc2185b</color> <!-- Material Pink 700 -->
    <color name="SIM_color_red">#ffd32f2f</color> <!-- Material Red 700 -->

    <!-- SIM colors updated for GAR -->
    <color name="SIM_color_cyan">#ff006D74</color> <!-- Material Custom Cyan -->
    <color name="SIM_color_blue800">#ff185ABC</color> <!-- Material Blue 800 -->
    <color name="SIM_color_green800">#ff137333</color> <!-- Material Green 800 -->
    <color name="SIM_color_purple800">#ff7627bb</color> <!-- Material Purple 800 -->
    <color name="SIM_color_pink800">#ffb80672</color> <!-- Material Pink 800 -->
    <color name="SIM_color_orange">#ff995400</color> <!-- Material Custom Orange -->

    <!-- Dark mode SIM colors -->
    <color name="SIM_dark_mode_color_cyan">#ff4DD0E1</color> <!-- Material Cyan 300 -->
    <color name="SIM_dark_mode_color_blue">#ff8AB4F8</color> <!-- Material Blue 300 -->
    <color name="SIM_dark_mode_color_green">#ff81C995</color> <!-- Material Green 300 -->
    <color name="SIM_dark_mode_color_purple">#ffC58AF9</color> <!-- Material Purple 300 -->
    <color name="SIM_dark_mode_color_pink">#ffff8bcb</color> <!-- Material Pink 300 -->
    <color name="SIM_dark_mode_color_orange">#fffcad70</color> <!-- Material Orange 300 -->

    <!-- Top app bar colors -->
    <color name="color_surface_header">@*android:color/surface_header_light</color>

    <!-- Accessibility Settings icon background colors -->
    <color name="accessibility_feature_background">#5F6368</color> <!-- Google grey 700 -->
    <color name="accessibility_magnification_background">#F50D60</color>
    <color name="accessibility_daltonizer_background">#00BCD4</color>
    <color name="accessibility_color_inversion_background">#546E7A</color>

    <color name="battery_info_error_color_red">#fce8e6</color> <!-- Material Red 50 -->
    <!-- Dialog error color. -->
    <color name="settings_dialog_colorError">#d93025</color> <!-- Red 600 -->

    <!-- Fingerprint enrollment color -->
    <color name="fingerprint_enrollment_finish_color_outline">#1A73E8</color>

    <!-- Side fingerprint sensor guided enrollment fill colors -->
    <color name="sfps_enrollment_fp_captured_color">#d2e3fc</color> <!-- Blue 100 -->
    <color name="sfps_enrollment_fp_error_color">#fad2cf</color> <!-- Red 100 -->
    <color name="sfps_enrollment_progress_bar_bg_color">#E8EAED</color> <!-- Gray 200 -->
    <color name="sfps_enrollment_progress_bar_fill_color">#1a73e8</color> <!-- Blue 600 -->
    <color name="sfps_enrollment_progress_bar_error_color">#d93025</color> <!-- Red 600 -->

    <!-- Material inverse ripple color, useful for inverted backgrounds. -->
    <color name="ripple_material_inverse">@*android:color/ripple_material_dark</color>

    <!-- Battery error text color -->
    <color name="battery_info_error_color_black">@*android:color/primary_text_default_material_light</color>

    <!-- Background for multiple user settings page avatars -->
    <color name="user_avatar_color_bg">?androidprv:attr/colorSurface</color>

    <!-- Icon tint color for battery usage system icon -->
    <color name="battery_usage_system_icon_color">?android:attr/textColorPrimary</color>

    <!-- Collapsing toolbar text color -->
    <color name="settingslib_text_color_primary_device_default">?android:attr/textColorPrimary</color>
</resources>
