<!-- Copyright (C) 2006 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources
    xmlns:androidprv="http://schemas.android.com/apk/prv/res/android">

    <style name="Widget.ActionBar.Base"
           parent="@android:style/Widget.DeviceDefault.Light.ActionBar.Solid"/>

    <style name="Widget.ActionBar"
           parent="Widget.ActionBar.Base">
        <item name="android:contentInsetStart">@dimen/actionbar_contentInsetStart</item>
    </style>

    <style name="Widget.ActionBar.SubSettings" parent="Widget.ActionBar">
        <item name="android:contentInsetStart">@dimen/actionbar_subsettings_contentInsetStart</item>
    </style>

    <style name="info_label">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textAppearance">@style/TextAppearance.info_label</item>
        <item name="android:paddingEnd">4dip</item>
    </style>

    <style name="info_value">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textAppearance">@style/TextAppearance.info_value</item>
    </style>

    <style name="info_small">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textAppearance">@style/TextAppearance.info_small</item>
    </style>

    <style name="info_layout">
        <item name="android:orientation">vertical</item>
        <item name="android:paddingStart">10dip</item>
        <item name="android:paddingTop">10dip</item>
        <item name="android:paddingEnd">10dip</item>
        <item name="android:paddingBottom">10dip</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="entry_layout">
        <item name="android:orientation">horizontal</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="form_value">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">match_parent</item>
    </style>

    <style name="PreferenceFragmentListSinglePane" parent="@*android:style/PreferenceFragmentList">
        <item name="android:layout_marginStart">0dp</item>
        <item name="android:layout_marginEnd">0dp</item>
        <item name="android:scrollbarStyle">outsideOverlay</item>
    </style>

    <style name="TrustedCredentialsList">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:scrollbarStyle">outsideOverlay</item>
    </style>

    <style name="bt_item">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">8dip</item>
        <item name="android:layout_marginStart">16dip</item>
        <item name="android:layout_marginEnd">16dip</item>
        <item name="android:orientation">vertical</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="bt_item_edit_content">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">18sp</item>
    </style>

    <style name="wifi_item">
        <item name="android:layout_marginTop">8dip</item>
        <item name="android:layout_marginStart">8dip</item>
        <item name="android:layout_marginEnd">8dip</item>
        <item name="android:paddingStart">8dip</item>
        <item name="android:paddingEnd">8dip</item>
        <item name="android:orientation">vertical</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="wifi_item_label">
        <item name="android:paddingStart">8dip</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@*android:style/TextAppearance.DeviceDefault.Body1</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="wifi_item_warning">
        <item name="android:paddingStart">8dip</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@*android:style/TextAppearance.DeviceDefault.Body1</item>
        <item name="android:textColor">?android:attr/colorError</item>
    </style>

    <style name="wifi_item_content">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@*android:style/TextAppearance.DeviceDefault.Subhead</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:minHeight">@dimen/min_tap_target_size</item>
    </style>

    <style name="wifi_item_spinner" parent="wifi_item_content">
        <item name="android:minHeight">48dp</item>
    </style>

    <style name="wifi_advanced_toggle" parent="wifi_item_content">
        <item name="android:background">@null</item>
        <item name="android:button">@null</item>
        <item name="android:drawableEnd">@drawable/ic_expand</item>
        <item name="android:paddingStart">8dip</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="wifi_item_edit_content">
        <item name="android:paddingStart">4dip</item>
        <item name="android:layout_marginStart">4dip</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@android:style/TextAppearance.DeviceDefault.Medium</item>
        <item name="android:textColorHint">?android:attr/textColorSecondary</item>
        <item name="android:minHeight">@dimen/min_tap_target_size</item>
        <item name="android:maxLength">500</item>
    </style>

    <style name="wifi_section">
        <item name="android:orientation">vertical</item>
    </style>

    <style name="adb_wireless_item">
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginStart">8dp</item>
        <item name="android:layout_marginEnd">8dp</item>
        <item name="android:paddingStart">8dp</item>
        <item name="android:paddingEnd">8dp</item>
        <item name="android:orientation">vertical</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="adb_wireless_item_label">
        <item name="android:paddingStart">8dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@android:style/TextAppearance.Material.Body1</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="adb_wireless_item_content">
        <item name="android:paddingStart">8dp</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@android:style/TextAppearance.Material.Body1</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="adb_wireless_item_progress_text">
        <item name="android:paddingTop">16dp</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@android:style/TextAppearance.Material.Body1</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="adb_wireless_section">
        <item name="android:orientation">vertical</item>
    </style>

    <style name="ConfirmDeviceCredentialsAnimationStyle"
           parent="@*android:style/Animation.Material.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/confirm_credential_open_enter</item>
        <item name="android:activityOpenExitAnimation">@anim/confirm_credential_open_exit</item>
    </style>

    <style name="SetupWizardButton.Negative" parent="@style/SudGlifButton.Secondary">
        <!-- Negative margin to offset for padding of the button itself. We want the label to be
             aligned with the text above it -->
        <item name="android:layout_marginStart">-16dp</item>
    </style>

    <style name="SetupWizardButton.Positive" parent="@style/SudGlifButton.Primary"/>

    <style name="AccentColorHighlightBorderlessButton">
        <item name="android:colorControlHighlight">?android:attr/colorAccent</item>
    </style>

    <style name="vpn_label">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textAppearance">?android:attr/textAppearanceSmall</item>
    </style>

    <style name="vpn_value">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMedium</item>
        <item name="android:singleLine">true</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:minHeight">@dimen/min_tap_target_size</item>
        <item name="android:textColorHint">?android:attr/textColorSecondary</item>
    </style>

    <style name="vpn_warning">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingStart">8dip</item>
        <item name="android:textAppearance">@android:style/TextAppearance.DeviceDefault.Small</item>
    </style>

    <style name="vpn_app_management_version_title">
        <item name="android:textAppearance">?android:attr/textAppearanceListItem</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="vpn_app_management_version_summary">
        <item name="android:textAppearance">?android:attr/textAppearanceListItemSecondary</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="TextAppearance" parent="android:TextAppearance.DeviceDefault"/>

    <style name="TextAppearance.info_label">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TextAppearance.info_small">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="TextAppearance.info_value">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="TextAppearance.PasswordEntry" parent="android:TextAppearance.DeviceDefault">
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">24sp</item>
    </style>

    <style name="TextAppearance.Medium" parent="@android:style/TextAppearance.DeviceDefault.Medium"/>
    <style name="TextAppearance.Small" parent="@android:style/TextAppearance.DeviceDefault.Small"/>
    <style name="TextAppearance.Switch"
           parent="@android:style/TextAppearance.DeviceDefault.Widget.ActionBar.Title">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TextAppearance.CategoryTitle"
           parent="@*android:style/TextAppearance.DeviceDefault.Body2">
        <item name="android:textAllCaps">true</item>
        <item name="android:textSize">11sp</item>
        <!-- 0.8 Spacing, 0.8/11 = 0.072727273 -->
        <item name="android:letterSpacing">0.072727273</item>
    </style>

    <style name="TextAppearance.SuggestionTitle"
           parent="@*android:style/TextAppearance.DeviceDefault.Subhead">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TextAppearance.SuggestionSummary" parent="TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="TextAppearance.ErrorText"
           parent="@*android:TextAppearance.DeviceDefault.Body1">
        <item name="android:textColor">?android:attr/colorError</item>
    </style>

    <style name="TextAppearance.Small.SwitchBar">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="TextAppearance.RemoveDialogContent"
           parent="@android:style/TextAppearance.DeviceDefault">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="TextAppearance.SearchBar">
        <item name="android:layout_gravity">center</item>
        <item name="android:fontFamily">@*android:string/config_headlineFontFamily</item>
        <item name="android:textSize">@dimen/search_bar_text_size</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="TextAppearance.HomepageCardTitle"
           parent="@*android:style/TextAppearance.DeviceDefault.Subhead">
        <item name="android:fontFamily">@*android:string/config_headlineFontFamilyMedium</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TextAppearance.ConditionCardSummary"
           parent="@*android:style/TextAppearance.DeviceDefault.Body1">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="TextAppearance.ZenOnboardingButton">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:attr/colorAccent</item>
        <item name="android:background">@*android:drawable/btn_borderless_rect</item>
        <item name="android:gravity">center</item>
        <item name="android:focusable">true</item>
        <item name="android:padding">8dp</item>
    </style>

    <style name="TextAppearance.NotificationHistory" parent="@android:style/Theme.DeviceDefault.DayNight">
        <item name="android:textSize">14sp</item>
    </style>

    <style name="TextAppearance.NotificationHistory.AppName">
        <item name="android:fontFamily">@*android:string/config_bodyFontFamily</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="TextAppearance.ContextualCardDismissalText"
           parent="@*android:style/TextAppearance.DeviceDefault.Body1">
        <item name="android:fontFamily">@*android:string/config_headlineFontFamilyMedium</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TextAppearance.DialogMessage"
           parent="@*android:style/TextAppearance.DeviceDefault.Body1">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="SuggestionCardText">
        <item name="android:textAlignment">viewStart</item>
    </style>

    <style name="SuggestionCardIcon">
        <item name="android:layout_centerHorizontal">false</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_marginStart">12dp</item>
        <item name="android:layout_marginEnd">12dp</item>
    </style>

    <style name="FingerprintLayoutTheme">
        <item name="android:icon">@drawable/ic_lock</item>
    </style>

    <style name="FaceLayoutTheme">
        <item name="android:icon">@drawable/ic_lock</item>
    </style>

    <style name="PreviewPagerPageIndicator">
        <item name="dotGap">8dp</item>
        <item name="pageIndicatorColor">?android:attr/colorControlNormal</item>
        <item name="currentPageIndicatorColor">?android:attr/colorControlActivated</item>
    </style>

    <style name="LanguageCheckboxAndLabel">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">?android:attr/listPreferredItemPaddingStart</item>
        <item name="android:minHeight">?android:attr/listPreferredItemHeight</item>
        <item name="android:textAppearance">?android:attr/textAppearanceListItem</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="BiometricHeaderStyle" parent="@*android:style/TextAppearance.DeviceDefault.Subhead">
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">24dp</item>
        <item name="android:lineSpacingMultiplier">1.2</item>
    </style>

    <style name="BiometricEnrollIntroTitle">
        <item name="android:paddingVertical">24dp</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMedium</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">20sp</item>
    </style>

    <style name="BiometricEnrollIntroMessage">
        <item name="android:lineSpacingMultiplier">1.2</item>
        <item name="android:paddingBottom">16dp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="RingProgressBarStyle"
           parent="android:style/Widget.Material.ProgressBar.Horizontal">
        <item name="android:indeterminate">false</item>
        <item name="android:max">10000</item>
        <item name="android:mirrorForRtl">false</item>
        <item name="android:progressDrawable">@drawable/ring_progress</item>
    </style>

    <style name="ActionPrimaryButton" parent="android:Widget.DeviceDefault.Button.Colored">
        <item name="android:theme">@style/RoundedCornerThemeOverlay</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@*android:string/config_bodyFontFamilyMedium</item>
    </style>
    <style name="ActionSecondaryButton" parent="android:Widget.DeviceDefault.Button"/>

    <style name="RoundedCornerThemeOverlay">
        <item name="android:buttonCornerRadius">24dp</item>
        <item name="android:paddingHorizontal">16dp</item>
        <item name="android:colorControlHighlight">@color/ripple_material_inverse</item>
    </style>

    <style name="LockPatternContainerStyle">
        <item name="android:gravity">center</item>
        <item name="android:maxHeight">@dimen/biometric_auth_pattern_view_max_size</item>
        <item name="android:maxWidth">@dimen/biometric_auth_pattern_view_max_size</item>
        <item name="android:minHeight">@dimen/biometric_auth_pattern_view_size</item>
        <item name="android:minWidth">@dimen/biometric_auth_pattern_view_size</item>
        <item name="android:paddingBottom">0dp</item>
        <item name="android:paddingHorizontal">0dp</item>
        <item name="android:paddingTop">0dp</item>
    </style>

    <style name="LockPatternStyle">
        <item name="*android:regularColor">?android:attr/colorAccent</item>
        <item name="*android:successColor">?android:attr/textColorPrimary</item>
        <item name="*android:errorColor">?android:attr/colorError</item>
        <item name="*android:dotColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="LockPatternButtonStyle" parent="@style/SudGlifButton.Tertiary">
        <item name="android:layout_marginStart">@dimen/sud_glif_button_margin_start</item>
        <item name="android:layout_marginEnd">@dimen/sud_glif_button_margin_end</item>
        <item name="android:layout_gravity">start|top</item>
    </style>

    <style name="device_info_dialog_label">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:fontFamily">@*android:string/config_headlineFontFamily</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="device_info_dialog_value">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:fontFamily">@*android:string/config_bodyFontFamily</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:paddingBottom">24dp</item>
    </style>

    <style name="ContextualCardStyle">
        <item name="android:layout_marginBottom">@dimen/contextual_card_vertical_margin</item>
        <item name="android:layout_marginStart">@dimen/contextual_card_side_margin</item>
        <item name="android:layout_marginEnd">@dimen/contextual_card_side_margin</item>
        <item name="cardBackgroundColor">?androidprv:attr/colorSurface</item>
        <item name="cardCornerRadius">@dimen/contextual_card_corner_radius</item>
        <item name="cardElevation">0dp</item>
        <item name="rippleColor">?android:attr/colorControlHighlight</item>
    </style>

    <style name="SearchBarStyle">
        <item name="cardCornerRadius">@dimen/search_bar_corner_radius</item>
        <item name="cardElevation">0dp</item>
    </style>

    <style name="ConditionCardBorderlessButton"
           parent="android:Widget.DeviceDefault.Button.Borderless">
        <item name="android:textColor">?android:attr/colorAccent</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="ConditionHalfCardBorderlessButton"
           parent="@style/ConditionCardBorderlessButton">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:layout_marginStart">4dp</item>
    </style>

    <style name="ConditionFullCardBorderlessButton"
           parent="@style/ConditionCardBorderlessButton">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:paddingStart">62dp</item>
        <item name="android:paddingEnd">50dp</item>
    </style>

    <style name="ContextualCardDismissalButton"
           parent="android:Widget.DeviceDefault.Button.Borderless.Colored">
        <item name="android:minWidth">24dp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="Widget.SliceView.Settings">
        <item name="titleSize">@*android:dimen/text_size_subhead_material</item>
        <item name="rowStyle">@style/SliceRow.Settings</item>
    </style>

    <style name="Widget.SliceView.ContextualCard">
        <item name="rowStyle">@style/SliceRow</item>
        <item name="android:background">@color/contextual_card_background</item>
    </style>

    <style name="Widget.SliceView.Panel">
        <item name="titleSize">16sp</item>
        <item name="rowStyle">@style/SliceRow</item>
        <item name="android:background">?android:attr/colorBackgroundFloating</item>
    </style>

    <style name="Widget.SliceView.Panel.Slider">
        <item name="rowStyle">@style/SliceRow.Slider</item>
    </style>

    <style name="Widget.SliceView.Bluetooth">
        <item name="titleSize">20dp</item>
        <item name="subtitleSize">14dp</item>
        <item name="headerTitleSize">20dp</item>
        <item name="headerSubtitleSize">14dp</item>
    </style>

    <style name="Widget.SliceView.Panel.Slider.LargeIcon">
        <item name="rowStyle">@style/SliceRow.Slider.LargeIcon</item>
    </style>

    <style name="SliceRow">
        <!-- 2dp start padding for the start icon -->
        <item name="titleItemStartPadding">2dp</item>
        <item name="titleItemEndPadding">0dp</item>

        <!-- Padding between content and the start icon is 14dp -->
        <item name="contentStartPadding">14dp</item>
        <!-- Padding between content and end items is 16dp -->
        <item name="contentEndPadding">16dp</item>

        <!-- Both side margins of end item are 16dp -->
        <item name="endItemStartPadding">16dp</item>
        <item name="endItemEndPadding">16dp</item>

        <!-- Both side margins of bottom divider are 12dp -->
        <item name="bottomDividerStartPadding">12dp</item>
        <item name="bottomDividerEndPadding">12dp</item>

        <item name="actionDividerHeight">32dp</item>
    </style>

    <style name="SliceRow.Settings">
        <!-- Padding between content and the start icon is 8dp -->
        <item name="contentStartPadding">8dp</item>
    </style>

    <style name="SliceRow.Slider">
        <!-- Padding between content and the start icon is 5dp -->
        <item name="contentStartPadding">5dp</item>
        <item name="contentEndPadding">0dp</item>

        <!-- 0dp start padding for the end item -->
        <item name="endItemStartPadding">0dp</item>
        <!-- 8dp end padding for the end item -->
        <item name="endItemEndPadding">8dp</item>

        <item name="titleSize">20sp</item>
        <!-- Align text with slider -->
        <item name="titleStartPadding">11dp</item>
        <item name="subContentStartPadding">11dp</item>

        <!-- Padding for indeterminate progress bar -->
        <item name="progressBarStartPadding">12dp</item>
        <item name="progressBarEndPadding">16dp</item>

        <!-- Title icon size -->
        <item name="iconSize">25dp</item>
    </style>

    <style name="SliceRow.Slider.LargeIcon">
        <!-- 10dp start padding for the start icon -->
        <item name="titleItemStartPadding">10dp</item>
        <!-- Title icon size -->
        <item name="iconSize">36dp</item>
        <!-- RecycleView item animator -->
        <item name="disableRecyclerViewItemAnimator">true</item>
    </style>

    <style name="DisclaimerPositiveButton" parent="@style/SudGlifButton.Primary">
        <item name="android:layout_margin">16dp</item>
        <item name="android:paddingStart">8dp</item>
        <item name="android:paddingEnd">8dp</item>
    </style>

    <style name="DisclaimerNegativeButton" parent="@style/SudGlifButton.Secondary">
        <item name="android:layout_margin">16dp</item>
        <item name="android:paddingStart">8dp</item>
        <item name="android:paddingEnd">8dp</item>
    </style>

    <style name="PanelOptionRoundedOutlinedButton" parent="@android:style/Widget.Material.Button">
        <item name="android:background">@drawable/volume_dialog_button_background_outline</item>
    </style>

    <style name="PanelOptionRoundedSolidButton" parent="@android:style/Widget.Material.Button">
        <item name="android:textColor">@android:color/system_neutral1_900</item>
        <item name="android:background">@drawable/volume_dialog_button_background_solid</item>
    </style>

    <style name="SetupWizardPartnerResource">
        <!-- Disable to use partner overlay theme for outside setupwizard flow. -->
        <item name="sucUsePartnerResource">@bool/config_suc_use_partner_resource</item>
        <!-- Enable heavy theme style inside setupwizard flow. -->
        <item name="sudUsePartnerHeavyTheme">true</item>
    </style>

    <style name="TextAppearance.NotificationImportanceDetail">
        <item name="android:textSize">@dimen/notification_importance_description_text</item>
        <item name="android:fontFamily">@*android:string/config_bodyFontFamily</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="TextAppearance.NotificationImportanceButton">
        <item name="android:textSize">@dimen/notification_importance_button_text</item>
        <item name="android:fontFamily">@*android:string/config_headlineFontFamilyMedium</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="TextAppearance.NotificationImportanceButton.Selected" parent="TextAppearance.NotificationImportanceButton">
        <item name="android:textColor">?android:attr/colorAccent</item>
    </style>

    <style name="TextAppearance.NotificationImportanceButton.Unselected" parent="TextAppearance.NotificationImportanceButton">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="AccessibilityDialog">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">vertical</item>
        <item name="android:divider">@*android:drawable/list_divider_material</item>
        <item name="android:showDividers">middle</item>
    </style>

    <style name="TextAppearance.Tab" parent="@android:style/TextAppearance.DeviceDefault.Medium">
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">@*android:string/config_headlineFontFamilyMedium</item>
    </style>

    <style name="AccessibilityDialogServiceIcon">
        <item name="android:layout_width">36dp</item>
        <item name="android:layout_height">36dp</item>
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:layout_marginBottom">16dp</item>
        <item name="android:scaleType">fitCenter</item>
    </style>

    <style name="AccessibilityDialogIcon">
        <item name="android:layout_width">18dp</item>
        <item name="android:layout_height">18dp</item>
        <item name="android:layout_marginEnd">12dp</item>
        <item name="android:scaleType">fitCenter</item>
    </style>

    <style name="AccessibilityDialogTitle"
           parent="@android:style/TextAppearance.DeviceDefault">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:fontFamily">@*android:string/config_headlineFontFamilyMedium</item>
    </style>

    <style name="AccessibilityDialogDescription"
           parent="@android:style/TextAppearance.DeviceDefault">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:layout_marginBottom">32dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="AccessibilityDialogPermissionTitle"
           parent="@android:style/TextAppearance.DeviceDefault">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:fontFamily">@*android:string/config_headlineFontFamily</item>
    </style>

    <style name="AccessibilityDialogPermissionDescription"
           parent="@android:style/TextAppearance.DeviceDefault">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="AccessibilityDialogButtonBarSpace">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="AccessibilityDialogButtonList">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">vertical</item>
        <item name="android:divider">@*android:drawable/list_divider_material</item>
        <item name="android:showDividers">middle</item>
    </style>

    <style name="AccessibilityDialogButton"
           parent="@*android:style/Widget.DeviceDefault.Button.ButtonBar.AlertDialog">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:paddingEnd">8dp</item>
        <item name="android:paddingStart">8dp</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
    </style>

    <style name="AccessibilityTextReadingPreviewTitle"
        parent="@android:style/TextAppearance.DeviceDefault.Widget.ActionBar.Title" >
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:padding">6dp</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="Widget.PopupWindow.Settings"
           parent="@android:style/Widget.DeviceDefault.PopupWindow">
        <item name="android:clipToPadding">true</item>
        <item name="android:clipChildren">true</item>
    </style>

    <style name="PickerDialogTheme.Settings" parent="@*android:style/ThemeOverlay.Material.Dialog.DatePicker">
        <item name="android:clipToPadding">true</item>
        <item name="android:clipChildren">true</item>
    </style>

    <style name="CrossProfileConsentDialogTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:fontFamily">google-sans-medium</item>
        <item name="android:paddingTop">36dp</item>
        <item name="android:paddingBottom">16dp</item>
    </style>

    <style name="CrossProfileConsentDialogDescription">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">36dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="CrossProfileConsentDialogIcon">
        <item name="android:layout_width">24dp</item>
        <item name="android:layout_height">24dp</item>
        <item name="android:antialias">true</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="CrossProfileConsentDialogSubTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="CrossProfileConsentDialogSubDescription">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="TextAppearance.HeadLineFontFamily"
           parent="@*android:style/TextAppearance.DeviceDefault.Title">
        <item name="android:fontFamily">@*android:string/config_headlineFontFamily</item>
    </style>

    <style name="TextAppearance.HeadLineFontFamily.Subhead"
           parent="@*android:style/TextAppearance.DeviceDefault.Subhead">
        <item name="android:fontFamily">@*android:string/config_headlineFontFamily</item>
    </style>

    <style name="HomepageTitleText" parent="CollapsingToolbarTitle.Expanded">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">@dimen/homepage_title_margin_bottom</item>
        <item name="android:layout_marginHorizontal">@dimen/homepage_title_margin_horizontal</item>
    </style>

    <!-- Collapsing toolbar title styles - reduced font sizes -->
    <style name="CollapsingToolbarTitle.Collapsed" parent="@android:style/TextAppearance.DeviceDefault.Widget.ActionBar.Title">
        <item name="android:fontFamily">@*android:string/config_headlineFontFamily</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/settingslib_text_color_primary_device_default</item>
    </style>

    <style name="CollapsingToolbarTitle.Expanded" parent="@android:style/TextAppearance.DeviceDefault.Headline">
        <item name="android:fontFamily">@*android:string/config_headlineFontFamily</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/settingslib_text_color_primary_device_default</item>
        <item name="android:layout_marginStart">@dimen/expanded_title_margin_start</item>
        <item name="android:layout_marginEnd">@dimen/expanded_title_margin_end</item>
        <item name="android:layout_marginBottom">@dimen/expanded_title_margin_bottom</item>
        <item name="android:layout_marginTop">@dimen/expanded_title_margin_top</item>
    </style>

    <style name="RequestManageCredentialsButtonPanel">
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:layout_alignParentBottom">true</item>
        <item name="android:background">?android:colorBackground</item>
    </style>

    <style name="RoundedCornerButtonTheme">
        <item name="android:buttonCornerRadius">50dp</item>
        <item name="android:paddingStart">20dp</item>
        <item name="android:paddingEnd">20dp</item>
        <item name="android:paddingTop">18dp</item>
        <item name="android:paddingBottom">18dp</item>
    </style>

    <style name="RequestManageCredentialsAllowButton" parent="@style/ActionPrimaryButton">
        <item name="android:fontFamily">google-sans-medium</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="RequestManageCredentialsDontAllowButton"
           parent="@style/Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:fontFamily">google-sans-medium</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="RequestManageCredentialsFab">
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:layout_marginBottom">12dp</item>
    </style>

    <style name="RequestManageCredentialsHeader">
        <item name="android:paddingStart">40dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">58dp</item>
        <item name="android:paddingBottom">24dp</item>
        <item name="android:orientation">vertical</item>
    </style>

    <style name="RequestManageCredentialsHeaderLandscape">
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">24dp</item>
    </style>

    <style name="RequestManageCredentialsTitle">
        <item name="android:layout_marginTop">24dp</item>
        <item name="android:textSize">36sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="RequestManageCredentialsDescription">
        <item name="android:layout_marginTop">24dp</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:alpha">0.7</item>
    </style>

    <style name="AppAuthenticationPolicyItem">
        <item name="android:paddingStart">40dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>

    <style name="AppAuthenticationPolicyIcon">
        <item name="android:layout_marginTop">30dp</item>
        <item name="android:layout_marginEnd">20dp</item>
    </style>

    <style name="AppAuthenticationPolicyText">
        <item name="android:maxLines">1</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="AppAuthenticationPolicyNumberOfUrisText">
        <item name="android:layout_marginTop">4dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:alpha">0.7</item>
    </style>

    <style name="AppAuthenticationExpander">
        <item name="android:layout_marginTop">24dp</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:scaleType">fitEnd</item>
    </style>

    <style name="AppUriAuthenticationPolicyText">
        <item name="android:maxLines">1</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="TextAppearance.AdminDialogTitle"
           parent="@*android:style/TextAppearance.DeviceDefault.Title">
        <item name="android:fontFamily">@*android:string/config_headlineFontFamily</item>
        <item name="android:textSize">24sp</item>
    </style>

    <style name="TextAppearance.AdminDialogMessage"
           parent="@*android:style/TextAppearance.DeviceDefault">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="TextAppearance.SimConfirmDialogList" parent="@style/TextAppearance.DialogMessage"/>

    <style name="TextAppearance.SimConfirmDialogList.Summary">
        <item name="android:textAppearance">?android:attr/textAppearanceSmall</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="SimConfirmDialog.OutlineButton" parent="@android:style/Widget.Material.Button">
        <item name="android:layout_marginStart">8dp</item>
        <item name="android:layout_height">36dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:lineHeight">20sp</item>
        <item name="android:fontFamily">@*android:string/config_bodyFontFamilyMedium</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:background">@drawable/sim_confirm_dialog_btn_outline</item>
    </style>

    <style name="SimConfirmDialog.ButtonBarStyle" parent="@android:style/Widget.Material.ButtonBar">
        <item name="android:paddingEnd">8dp</item>
    </style>

    <style name="DreamCardStyle">
        <item name="cardBackgroundColor">@color/dream_card_color_state_list</item>
        <item name="cardCornerRadius">@dimen/dream_item_corner_radius</item>
        <item name="cardElevation">0dp</item>
        <item name="rippleColor">?android:attr/colorControlHighlight</item>
        <item name="contentPadding">@dimen/dream_item_content_padding</item>
    </style>

    <style name="BroadcastActionButton" parent="@android:style/Widget.Material.Button">
        <item name="android:background">@drawable/broadcast_button_outline</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:lineHeight">20sp</item>
        <item name="android:fontFamily">@*android:string/config_bodyFontFamilyMedium</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="QrCodeScanner">
        <item name="android:fontFamily">@*android:string/config_headlineFontFamily</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textDirection">locale</item>
    </style>

    <style name="GroupOptionsButton" parent="android:Widget.DeviceDefault.Button">
        <item name="android:drawablePadding">4dp</item>
        <item name="android:layout_marginEnd">8dp</item>
        <item name="android:paddingTop">20dp</item>
        <item name="android:paddingBottom">20dp</item>
    </style>

    <style name="CardPreferencePrimaryButton" parent="@style/ActionPrimaryButton">
        <item name="android:fontFamily">google-sans-medium</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:singleLine">true</item>
        <item name="android:paddingHorizontal">24dp</item>
    </style>

    <style name="CardPreferenceBorderlessButton"
        parent="@style/Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:fontFamily">google-sans-medium</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:singleLine">true</item>
    </style>
</resources>
