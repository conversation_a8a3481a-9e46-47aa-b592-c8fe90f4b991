/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _NDK_MEDIA_ERROR_PRIV_H
#define _NDK_MEDIA_ERROR_PRIV_H

#include <media/NdkMediaError.h>
#include <utils/Errors.h>

media_status_t translate_error(android::status_t);

android::status_t reverse_translate_error(media_status_t);

#endif // _NDK_MEDIA_ERROR_PRIV_H
