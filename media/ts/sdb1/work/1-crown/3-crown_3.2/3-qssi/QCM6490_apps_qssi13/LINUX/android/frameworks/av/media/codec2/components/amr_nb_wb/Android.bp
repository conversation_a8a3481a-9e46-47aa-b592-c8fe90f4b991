package {
    default_applicable_licenses: [
        "frameworks_av_media_codec2_components_amr_nb_wb_license",
    ],
}

// Added automatically by a large-scale-change
// See: http://go/android-license-faq
license {
    name: "frameworks_av_media_codec2_components_amr_nb_wb_license",
    visibility: [":__subpackages__"],
    license_kinds: [
        "SPDX-license-identifier-Apache-2.0",
    ],
    license_text: [
        "NOTICE",
    ],
}

cc_library {
    name: "libcodec2_soft_amrnbdec",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_all-defaults",
    ],

    srcs: ["C2SoftAmrDec.cpp"],

    cflags: [
        "-DAMRNB",
    ],

    static_libs: [
        "libstagefright_amrnbdec",
        "libstagefright_amrwbdec",
    ],

    shared_libs: [
        "libstagefright_amrnb_common",
    ],
}

cc_library {
    name: "libcodec2_soft_amrwbdec",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_all-defaults",
    ],

    srcs: ["C2SoftAmrDec.cpp"],

    static_libs: [
        "libstagefright_amrnbdec",
        "libstagefright_amrwbdec",
    ],

    shared_libs: [
        "libstagefright_amrnb_common",
    ],
}

cc_library {
    name: "libcodec2_soft_amrnbenc",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_all-defaults",
    ],

    srcs: ["C2SoftAmrNbEnc.cpp"],

    static_libs: [
        "libstagefright_amrnbenc",
    ],

    shared_libs: [
        "libstagefright_amrnb_common",
    ],
}

cc_library {
    name: "libcodec2_soft_amrwbenc",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_all-defaults",
    ],

    srcs: ["C2SoftAmrWbEnc.cpp"],

    static_libs: [
        "libstagefright_amrwbenc",
    ],

    shared_libs: [
        "libstagefright_enc_common",
    ],
}
