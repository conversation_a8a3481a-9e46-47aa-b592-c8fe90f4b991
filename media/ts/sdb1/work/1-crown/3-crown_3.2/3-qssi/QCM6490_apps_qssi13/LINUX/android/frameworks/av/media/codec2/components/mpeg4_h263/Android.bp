package {
    default_applicable_licenses: [
        "frameworks_av_media_codec2_components_mpeg4_h263_license",
    ],
}

// Added automatically by a large-scale-change
// See: http://go/android-license-faq
license {
    name: "frameworks_av_media_codec2_components_mpeg4_h263_license",
    visibility: [":__subpackages__"],
    license_kinds: [
        "SPDX-license-identifier-Apache-2.0",
    ],
    license_text: [
        "NOTICE",
    ],
}

cc_library {
    name: "libcodec2_soft_mpeg4dec",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_signed-defaults",
    ],

    srcs: ["C2SoftMpeg4Dec.cpp"],

    static_libs: ["libstagefright_m4vh263dec"],

    cflags: [
        "-DOSCL_IMPORT_REF=",
        "-DMPEG4",
    ],
}

cc_library {
    name: "libcodec2_soft_h263dec",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_signed-defaults",
    ],

    srcs: ["C2SoftMpeg4Dec.cpp"],

    static_libs: ["libstagefright_m4vh263dec"],

    cflags: [
        "-DOSCL_IMPORT_REF=",
    ],
}

cc_library {
    name: "libcodec2_soft_mpeg4enc",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_signed-defaults",
    ],


    srcs: ["C2SoftMpeg4Enc.cpp"],

    static_libs: ["libstagefright_m4vh263enc"],

    cflags: [
        "-DMPEG4",
        "-DOSCL_IMPORT_REF=",
    ],
}

cc_library {
    name: "libcodec2_soft_h263enc",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_signed-defaults",
    ],

    srcs: ["C2SoftMpeg4Enc.cpp"],

    static_libs: [ "libstagefright_m4vh263enc" ],

    cflags: [
        "-DOSCL_IMPORT_REF=",
    ],
}
