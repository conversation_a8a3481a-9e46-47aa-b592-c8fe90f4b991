package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_library {
    name: "libcodec2_soft_hevcdec",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_signed-defaults",
        "libcodec2_soft_sanitize_cfi-defaults",
    ],

    srcs: ["C2SoftHevcDec.cpp"],

    static_libs: ["libhevcdec"],

}

cc_library {
    name: "libcodec2_soft_hevcenc",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_signed-defaults",
        "libcodec2_soft_sanitize_cfi-defaults",
    ],

    srcs: ["C2SoftHevcEnc.cpp"],

    static_libs: ["libhevcenc"],

}
