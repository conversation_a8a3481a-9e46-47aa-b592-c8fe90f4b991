// DO NOT DEPEND ON THIS DIRECTLY
// use libcodec2_soft-defaults instead
package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_library_headers {
    name: "libcodec2_soft_common_headers",
    defaults: ["libcodec2-impl-defaults"],
    vendor_available: true,

    export_include_dirs: [
        "include",
    ],
}

cc_library {
    name: "libcodec2_soft_common",
    defaults: ["libcodec2-impl-defaults"],
    vendor_available: true,

    srcs: [
        "SimpleC2Component.cpp",
        "SimpleC2Interface.cpp",
    ],

    export_include_dirs: [
        "include",
    ],

    export_shared_lib_headers: [
        "libsfplugin_ccodec_utils",
    ],

    header_libs: [
        "libarect_headers",
        "libnativewindow_headers",
    ],

    shared_libs: [
        "libcutils", // for properties
        "liblog", // for ALOG
        "libsfplugin_ccodec_utils", // for ImageCopy
        "libstagefright_foundation", // for Mutexed
    ],

    min_sdk_version: "29",
    apex_available: [
        "//apex_available:platform",
        "com.android.media.swcodec",
    ],

    sanitize: {
        misc_undefined: [
            "unsigned-integer-overflow",
            "signed-integer-overflow",
        ],
        cfi: true,
    },

    ldflags: ["-Wl,-Bsymbolic"],
}

filegroup {
    name: "codec2_soft_exports",
    srcs: ["exports.lds"],
}

// public dependency for software codec implementation
// to be used by code under media/codecs/* only as its stability is not guaranteed
cc_defaults {
    name: "libcodec2_soft-defaults",
    defaults: ["libcodec2-impl-defaults"],
    vendor_available: true,
    version_script: ":codec2_soft_exports",
    export_shared_lib_headers: [
        "libsfplugin_ccodec_utils",
    ],

    header_libs: [
        "libarect_headers",
        "libnativewindow_headers",
    ],

    shared_libs: [
        "libcodec2_soft_common",
        "libcutils", // for properties
        "liblog", // for ALOG
        "libsfplugin_ccodec_utils", // for ImageCopy
        "libstagefright_foundation", // for ColorUtils and MIME
    ],

    cflags: [
        "-Wall",
        "-Werror",
    ],

    ldflags: ["-Wl,-Bsymbolic"],

    min_sdk_version: "29",
    apex_available: [
        "//apex_available:platform",
        "com.android.media.swcodec",
    ],
}

// public dependency for software codec implementation
// to be used by code under media/codecs/* only
cc_defaults {
    name: "libcodec2_soft_sanitize_all-defaults",

    sanitize: {
        misc_undefined: [
            "unsigned-integer-overflow",
            "signed-integer-overflow",
        ],
        cfi: true,
    },
}

// public dependency for software codec implementation
// to be used by code under media/codecs/* only
cc_defaults {
    name: "libcodec2_soft_sanitize_signed-defaults",

    sanitize: {
        misc_undefined: [
            "signed-integer-overflow",
        ],
    },
}

cc_defaults {
    name: "libcodec2_soft_sanitize_cfi-defaults",

    sanitize: {
        cfi: true,
        config: {
            cfi_assembly_support: true,
        },
    },
}

// TEMP: used by cheets2 project - remove when no longer used
cc_library {
    name: "libcodec2_simple_component",
    vendor_available: true,

    srcs: [
        "SimpleC2Interface.cpp",
    ],

    local_include_dirs: [
        "include",
    ],

    export_include_dirs: [
        "include",
    ],

    shared_libs: [
        "libcodec2",
        "libcodec2_vndk",
        "libcutils",
        "liblog",
        "libstagefright_foundation",
        "libutils",
    ],

    sanitize: {
        misc_undefined: [
            "unsigned-integer-overflow",
            "signed-integer-overflow",
        ],
        cfi: true,
    },

    ldflags: ["-Wl,-Bsymbolic"],
}
