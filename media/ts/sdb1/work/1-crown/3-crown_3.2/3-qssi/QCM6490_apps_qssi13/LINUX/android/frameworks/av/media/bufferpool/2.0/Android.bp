package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_defaults {
    name: "libstagefright_bufferpool@2.0-default",
    srcs: [
        "Accessor.cpp",
        "AccessorImpl.cpp",
        "BufferPoolClient.cpp",
        "BufferStatus.cpp",
        "ClientManager.cpp",
        "Connection.cpp",
        "Observer.cpp",
    ],
    export_include_dirs: [
        "include",
    ],
    shared_libs: [
        "libcutils",
        "libfmq",
        "libhidlbase",
        "liblog",
        "libutils",
        "android.hardware.media.bufferpool@2.0",
    ],
    export_shared_lib_headers: [
        "libfmq",
        "android.hardware.media.bufferpool@2.0",
    ],
}

cc_library {
    name: "libstagefright_bufferpool@2.0.1",
    defaults: ["libstagefright_bufferpool@2.0-default"],
    vendor_available: true,
    min_sdk_version: "29",
    apex_available: [
        "//apex_available:platform",
        "com.android.media.swcodec",
        "test_com.android.media.swcodec",
    ],

    // TODO: b/147147992
    double_loadable: true,
    cflags: [
        "-DBUFFERPOOL_CLONE_HANDLES",
    ],
}

// Deprecated. Do not use. Use libstagefright_bufferpool@2.0.1 instead.
cc_library {
    name: "libstagefright_bufferpool@2.0",
    defaults: ["libstagefright_bufferpool@2.0-default"],
    vendor_available: true,
    // TODO: b/147147992
    double_loadable: true,
    vndk: {
        enabled: true,
    },
}
