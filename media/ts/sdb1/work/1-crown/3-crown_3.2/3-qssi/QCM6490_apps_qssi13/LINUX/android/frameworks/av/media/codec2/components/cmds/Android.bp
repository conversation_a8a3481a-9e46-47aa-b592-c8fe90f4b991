package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_binary {
    name: "codec2play",
    defaults: ["libcodec2-impl-defaults"],

    srcs: [
        "codec2.cpp",
    ],

    header_libs: [
        "libmediadrm_headers",
    ],

    shared_libs: [
        "libbase",
        "libbinder",
        "libcutils",
        "libdatasource",
        "libgui",
        "liblog",
        "libstagefright",
        "libstagefright_foundation",
        "libui",
        "libutils",
    ],

    cflags: [
        "-Werror",
        "-Wall",
    ],

    sanitize: {
        cfi: true,
        misc_undefined: [
            "unsigned-integer-overflow",
            "signed-integer-overflow",
        ],
    },
}
