package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_defaults {
    name: "C2SoftCodecTest-defaults",
    defaults: [ "libcodec2-static-defaults" ],
    gtest: true,
    host_supported: false,
    srcs: [
        "C2SoftCodecTest.cpp",
    ],

    cflags: [
        "-Wall",
        "-Werror",
    ],
}

cc_test {
    name: "C2SoftMpeg4DecTest",
    defaults: ["C2SoftCodecTest-defaults"],

    static_libs: [
        "libstagefright_m4vh263dec",
        "libcodec2_soft_mpeg4dec",
    ],

    test_suites: [
        "general-tests",
    ],
}
