/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_test {
    name: "VtsVndkHidlBufferpoolV2_0TargetSingleTest",
    test_suites: ["device-tests"],
    defaults: ["VtsHalTargetTestDefaults"],
    srcs: [
        "allocator.cpp",
        "single.cpp",
    ],
    static_libs: [
        "android.hardware.media.bufferpool@2.0",
        "libcutils",
        "libstagefright_bufferpool@2.0.1",
    ],
    shared_libs: [
        "libfmq",
    ],
    compile_multilib: "both",
}

cc_test {
    name: "VtsVndkHidlBufferpoolV2_0TargetMultiTest",
    test_suites: ["device-tests"],
    defaults: ["VtsHalTargetTestDefaults"],
    srcs: [
        "allocator.cpp",
        "multi.cpp",
    ],
    static_libs: [
        "android.hardware.media.bufferpool@2.0",
        "libcutils",
        "libstagefright_bufferpool@2.0.1",
    ],
    shared_libs: [
        "libfmq",
    ],
    compile_multilib: "both",
}

cc_test {
    name: "VtsVndkHidlBufferpoolV2_0TargetCondTest",
    test_suites: ["device-tests"],
    defaults: ["VtsHalTargetTestDefaults"],
    srcs: [
        "allocator.cpp",
        "cond.cpp",
    ],
    static_libs: [
        "android.hardware.media.bufferpool@2.0",
        "libcutils",
        "libstagefright_bufferpool@2.0.1",
    ],
    shared_libs: [
        "libhidlbase",
        "libfmq",
    ],
    compile_multilib: "both",
}

cc_test {
    name: "BufferpoolUnitTest",
    test_suites: ["device-tests"],
    defaults: ["VtsHalTargetTestDefaults"],
    srcs: [
        "allocator.cpp",
        "BufferpoolUnitTest.cpp",
    ],
    static_libs: [
        "android.hardware.media.bufferpool@2.0",
        "libcutils",
        "libstagefright_bufferpool@2.0.1",
    ],
    shared_libs: [
        "libfmq",
    ],
    compile_multilib: "both",
}
