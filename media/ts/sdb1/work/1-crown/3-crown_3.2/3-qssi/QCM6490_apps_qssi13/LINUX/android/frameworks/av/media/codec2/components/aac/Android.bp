package {
    default_applicable_licenses: [
        "frameworks_av_media_codec2_components_aac_license",
    ],
}

// Added automatically by a large-scale-change
// See: http://go/android-license-faq
license {
    name: "frameworks_av_media_codec2_components_aac_license",
    visibility: [":__subpackages__"],
    license_kinds: [
        "SPDX-license-identifier-Apache-2.0",
    ],
    license_text: [
        "NOTICE",
    ],
}

cc_library {
    name: "libcodec2_soft_aacdec",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_all-defaults",
    ],

    srcs: [
        "C2SoftAacDec.cpp",
        "DrcPresModeWrap.cpp",
    ],

    static_libs: [
        "libFraunhoferAAC",
    ],
}

cc_library {
    name: "libcodec2_soft_aacenc",
    defaults: [
        "libcodec2_soft-defaults",
        "libcodec2_soft_sanitize_all-defaults",
    ],

    srcs: ["C2SoftAacEnc.cpp"],

    static_libs: [
        "libFraunhoferAAC",
    ],
}
