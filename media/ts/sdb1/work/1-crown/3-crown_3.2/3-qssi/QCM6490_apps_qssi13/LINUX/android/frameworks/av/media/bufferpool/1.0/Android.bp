package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_library {
    name: "libstagefright_bufferpool@1.0",
    vendor_available: true,
    srcs: [
        "Accessor.cpp",
        "AccessorImpl.cpp",
        "BufferPoolClient.cpp",
        "BufferStatus.cpp",
        "ClientManager.cpp",
        "Connection.cpp",
    ],
    export_include_dirs: [
        "include",
    ],
    shared_libs: [
        "libcutils",
        "libfmq",
        "libhidlbase",
        "liblog",
        "libutils",
        "android.hardware.media.bufferpool@1.0",
    ],
    export_shared_lib_headers: [
        "libfmq",
        "android.hardware.media.bufferpool@1.0",
    ],
}
